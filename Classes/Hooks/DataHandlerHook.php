<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Hooks;

use Bgs\FlightLandingPages\Service\SlugUpdateService;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * DataHandler hook for automatic slug updates
 *
 * Handles automatic updates of flight route and landing page slugs
 * when parent page slugs are changed.
 */
class DataHandlerHook
{
    protected SlugUpdateService $slugUpdateService;

    public function __construct()
    {
        $this->slugUpdateService = GeneralUtility::makeInstance(SlugUpdateService::class);
    }
    /**
     * Hook that is called after database operations
     *
     * @param string $status Operation status (new, update)
     * @param string $table Table name
     * @param string|int $id Record ID
     * @param array $fieldArray Field values
     * @param DataHandler $dataHandler DataHandler instance
     */
    public function processDatamap_afterDatabaseOperations(
        string $status,
        string $table,
        $id,
        array $fieldArray,
        DataHandler $dataHandler
    ): void {
        // Only process page updates where slug has changed
        if ($table !== 'pages' || $status !== 'update' || !isset($fieldArray['slug'])) {
            return;
        }

        $pageId = (int)$id;
        if ($pageId <= 0) {
            return;
        }

        // Get the old slug to compare
        $oldSlug = $this->getPageSlug($pageId);
        $newSlug = $fieldArray['slug'];

        // Only proceed if slug actually changed
        if ($oldSlug === $newSlug) {
            return;
        }

        // Update child landing pages and their flight routes
        $stats = $this->slugUpdateService->updateChildSlugs($pageId, $oldSlug, $newSlug);

        // Log results if needed (could be extended with proper logging)
        if (!empty($stats['errors'])) {
            // Handle errors if needed
        }
    }

    /**
     * Get current slug for a page
     *
     * @param int $pageId Page ID
     * @return string Current slug
     */
    protected function getPageSlug(int $pageId): string
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('slug')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery()
            ->fetchAssociative();

        return $result['slug'] ?? '';
    }
}
