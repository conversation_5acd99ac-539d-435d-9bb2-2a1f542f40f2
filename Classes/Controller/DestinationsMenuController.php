<?php
namespace Bgs\FlightLandingPages\Controller;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use Bgs\FlightLandingPages\Domain\Repository\FlightRouteRepository;

class DestinationsMenuController extends ActionController
{
    protected FlightRouteRepository $flightRouteRepository;

    public function injectFlightRouteRepository(FlightRouteRepository $flightRouteRepository): void
    {
        $this->flightRouteRepository = $flightRouteRepository;
    }

    /**
     * List all available flight destinations for the current site
     */
    public function listAction(): ResponseInterface
    {
        // Get display mode from FlexForm
        $displayMode = $this->settings['displayMode'] ?? 'list';
        $showOriginFilter = (bool)($this->settings['showOriginFilter'] ?? false);
        $showDestinationFilter = (bool)($this->settings['showDestinationFilter'] ?? false);

        // Get storage page for debugging/display purposes
        $storagePid = $this->getStoragePid();

        // Get all active routes - this automatically respects the Record Storage Page setting
        // If no Record Storage Page is set, it falls back to the current page
        $routes = $this->flightRouteRepository->findAllActive();

        // Get unique origins and destinations for filters
        $origins = [];
        $destinations = [];
        foreach ($routes as $route) {
            $origins[$route->getOriginCode()] = $route->getOriginName();
            $destinations[$route->getDestinationCode()] = $route->getDestinationName();
        }

        $this->view->assignMultiple([
            'routes' => $routes,
            'displayMode' => $displayMode,
            'showOriginFilter' => $showOriginFilter,
            'showDestinationFilter' => $showDestinationFilter,
            'origins' => $origins,
            'destinations' => $destinations,
            'storagePid' => $storagePid
        ]);

        return $this->htmlResponse();
    }

    /**
     * Get the effective storage page ID for debugging/display purposes
     * This shows which page is actually being used for record storage
     */
    protected function getStoragePid(): int
    {
        // Get the repository's default query settings which include the storagePid
        // This automatically respects the "Record Storage Page" field from the plugin
        $querySettings = $this->flightRouteRepository->createQuery()->getQuerySettings();
        $storagePids = $querySettings->getStoragePageIds();

        // If storage page is configured and not empty, use the first one
        if (!empty($storagePids) && !in_array(0, $storagePids, true)) {
            return (int)$storagePids[0];
        }

        // Fallback to current page ID (where the plugin is placed)
        return (int)$GLOBALS['TSFE']->id;
    }
}
